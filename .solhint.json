{"extends": "solhint:recommended", "plugins": ["prettier"], "rules": {"no-unused-import": "warn", "imports-on-top": "error", "no-global-import": "error", "gas-custom-errors": "off", "gas-small-strings": "warn", "gas-struct-packing": "warn", "no-unused-vars": "error", "code-complexity": ["error", 12], "compiler-version": ["error", "^0.8.0"], "const-name-snakecase": "error", "immutable-vars-naming": "off", "avoid-low-level-calls": "off", "event-name-camelcase": "error", "constructor-syntax": "error", "func-name-mixedcase": "off", "func-param-name-mixedcase": "off", "modifier-name-mixedcase": "error", "private-vars-leading-underscore": "off", "var-name-mixedcase": "off", "func-visibility": ["error", {"ignoreConstructors": true}], "not-rely-on-time": "off", "no-empty-blocks": "off", "contract-name-camelcase": "off", "no-inline-assembly": "off", "prettier/prettier": ["error", {"arrowParens": "avoid", "bracketSpacing": true, "endOfLine": "auto", "printWidth": 120, "useTabs": false, "singleQuote": false, "tabWidth": 4, "trailingComma": "all", "explicitTypes": "always"}], "reason-string": ["warn", {"maxLength": 64}], "ordering": "warn"}}