// SPDX-License-Identifier: MIT
pragma solidity ^0.8.25;

import { console2 } from "forge-std/Test.sol";
import { Script } from "forge-std/Script.sol";
import { PaymentSplitterFactory } from "src/factories/PaymentSplitterFactory.sol";
import { PaymentSplitter } from "src/core/PaymentSplitter.sol";

contract DeployPaymentSplitter is Script {
    address public paymentSplitter;

    // Default configuration if not overridden by environment variables
    address[] public defaultPayees = [
        ******************************************, // Default anvil address 1
        ******************************************, // Default anvil address 2
        ****************************************** // Default anvil address 3
    ];

    // Get existing factory address from environment or use default
    address factoryAddress = ******************************************;

    // Optional initial ETH amount
    uint256 initialEthAmount = 0;

    string[] public defaultPayeeNames = ["GrantRoundOperator", "ESF", "OpEx"];

    uint256[] public defaultShares = [50, 30, 20];

    function run() public returns (address) {
        require(factoryAddress != address(0), "Factory address must be set");

        // Use either environment variables or defaults
        address[] memory payees;
        string[] memory payeeNames;
        uint256[] memory shares;

        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        vm.startBroadcast(deployerPrivateKey);

        PaymentSplitterFactory factory = PaymentSplitterFactory(factoryAddress);

        // Deploy with or without ETH
        if (initialEthAmount > 0) {
            paymentSplitter = factory.createPaymentSplitterWithETH{ value: initialEthAmount }(
                payees,
                payeeNames,
                shares
            );
        } else {
            paymentSplitter = factory.createPaymentSplitter(payees, payeeNames, shares);
        }

        console2.log("PaymentSplitter deployed at:", paymentSplitter);

        vm.stopBroadcast();

        return paymentSplitter;
    }
}
