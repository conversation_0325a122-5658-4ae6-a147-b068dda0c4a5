{"name": "@golemfoundation/octant-v2-core", "version": "0.6.0-develop.0", "description": "", "packageManager": "yarn@4.9.2+sha512.1fc009bc09d13cfd0e19efa44cbfc2b9cf6ca61482725eb35bbc5e257e093ebf4130db6dfe15d604ff4b79efd8e1e8e99b25fa7d0a6197c9f9826358d4d65c3c", "repository": {"type": "git", "url": "https://github.com/golemfoundation/octant-v2-core.git"}, "bugs": {"url": "https://github.com/golemfoundation/octant-v2-core/issues"}, "homepage": "https://github.com/golemfoundation/octant-v2-core#readme", "engines": {"npm": "please-use-yarn", "node": ">=22.16.0"}, "files": ["out", "script", "src", "test", "./foundry.toml", "./remappings.txt"], "scripts": {"format": "prettier --config .prettierrc --plugin=prettier-plugin-solidity '{script,src,test,verification}/**/*.sol' -w", "format:check": "prettier --config .prettierrc --plugin=prettier-plugin-solidity '{script,src,test,verification}/**/*.sol' -c", "lint": "solhint './{src,scripts,test}/**/*.sol'", "precommit": "lint-staged", "build": "forge clean && forge compile --sizes", "build:contracts": "forge clean && forge compile --sizes --skip test --skip script && ./scripts/combine-proxy-abis.sh", "test": "forge clean && forge test", "test:ci": "forge clean && mkdir -p reports && (forge test --junit > reports/junit.xml || forge test --summary)", "gas": "forge snapshot --isolate --gas-report --diff .gas-snapshot > gasreport.txt", "coverage": "forge coverage --ir-minimum --report lcov --no-match-coverage '(test|mock|node_modules|script)'", "coverage:genhtml": "genhtml lcov.info --branch-coverage --output-dir coverage --ignore-errors inconsistent,corrupt,category", "slither:setup": "python3 -m venv .venv && .venv/bin/pip3 install slither-analyzer", "slither": ".venv/bin/slither . --print human-summary", "slither:ci": "slither . --print human-summary --disable-color 2> reports/slither.txt; slither .", "storage:generate": "./script/storage.sh generate", "storage:check": "./script/storage.sh check", "patch:hats": "sed -i.bak '197,205s/^/\\/\\/ /' dependencies/hats-protocol-1.0/src/Hats.sol", "init": "husky && shx chmod +x .husky/*", "deploy:tenderly": "bash -c 'source .env; forge script script/deployment/staging/DeployProtocol.s.sol --slow -f $RPC_URL && forge script script/deployment/staging/DeployProtocol.s.sol --slow --verify --verifier custom --broadcast -f $RPC_URL'", "release": "release-it", "lint:commits": "commitlint --last --verbose"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"dotenv": "^16.5.0"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@release-it/conventional-changelog": "^8.0.2", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "^3.5.3", "prettier-plugin-solidity": "^1.4.2", "release-it": "^18.1.2", "shx": "^0.3.4", "solhint": "^5.0.5", "solhint-plugin-prettier": "^0.1.0", "typescript": "5.3.3"}, "publishConfig": {"access": "restricted", "provenance": true, "registry": "https://npm.pkg.github.com"}}