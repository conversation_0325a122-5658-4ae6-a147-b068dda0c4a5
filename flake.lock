{"nodes": {"flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "flake-utils_2": {"locked": {"lastModified": 1644229661, "narHash": "sha256-1YdnJAsNy69bpcjuoKdOYQX0YxZBiCYZo4Twxerqv7k=", "owner": "numtide", "repo": "flake-utils", "rev": "3cecb5b042f7f209c56ffd8371b2711a290ec797", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "foundry": {"inputs": {"flake-utils": "flake-utils_2", "nixpkgs": "nixpkgs"}, "locked": {"lastModified": 1737993222, "narHash": "sha256-K0ImLNtYE7DxiDvF6iOLAP4zpsWI1OnNdtDtMu9SfSs=", "owner": "shazow", "repo": "foundry.nix", "rev": "ca0fd680ad9eba8623614af71cda00b6b3334ee9", "type": "github"}, "original": {"owner": "shazow", "ref": "monthly", "repo": "foundry.nix", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1666753130, "narHash": "sha256-Wff1dGPFSneXJLI2c0kkdWTgxnQ416KE6X4KnFkgPYQ=", "owner": "NixOS", "repo": "nixpkgs", "rev": "f540aeda6f677354f1e7144ab04352f61aaa0118", "type": "github"}, "original": {"id": "nixpkgs", "type": "indirect"}}, "nixpkgs_2": {"locked": {"lastModified": 1738172511, "narHash": "sha256-v/8yB8LIy/KsySZYN6rO/f4kCEkj+fTLkzR0TaNMB+w=", "owner": "nixos", "repo": "nixpkgs", "rev": "762a398892576efcc76fb233befbd58c2cef59e0", "type": "github"}, "original": {"owner": "nixos", "ref": "master", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"flake-utils": "flake-utils", "foundry": "foundry", "nixpkgs": "nixpkgs_2"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}