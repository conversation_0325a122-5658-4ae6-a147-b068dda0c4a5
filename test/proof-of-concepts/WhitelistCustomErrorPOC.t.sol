// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import { Whitelist } from "src/utils/Whitelist.sol";

/**
 * @title Whitelist Custom Error Proof of Concept
 * @dev Documents unexpected behavior with custom errors in require statements
 *
 * ORIGINAL ASSUMPTION: Custom error EmptyArray() cannot be used as second argument to require()
 * LOCATION: src/utils/Whitelist.sol lines 28 and 57
 * EXPECTED PROBLEM: require(condition, EmptyArray()) - EmptyArray() should not be a valid string
 * EXPECTED CORRECT: revert EmptyArray() - custom errors should only work with revert
 *
 * ACTUAL FINDINGS:
 * - Solidity 0.8.30 DOES compile require(condition, EmptyArray())
 * - The custom error IS thrown correctly with proper selector 0x521299a9
 * - This appears to be a newer Solidity feature or compiler enhancement
 *
 * CONCLUSION: The code works as intended, but this usage pattern is unusual
 * and may not be widely known or documented. Standard practice is still
 * to use revert statements with custom errors for clarity.
 */
contract WhitelistCustomErrorPOCTest is Test {
    Whitelist whitelist;
    address owner;

    function setUp() public {
        owner = makeAddr("owner");
        vm.prank(owner);
        whitelist = new Whitelist();
    }

    /**
     * @dev This test demonstrates the expected behavior when EmptyArray custom error
     * is properly used with revert statement instead of require statement.
     * 
     * The current Whitelist.sol contract has:
     * - Line 28: require(accounts.length > 0, EmptyArray());
     * - Line 57: require(accounts.length > 0, EmptyArray());
     * 
     * These should be changed to:
     * - if (accounts.length == 0) revert EmptyArray();
     * 
     * This test shows what the proper error handling should look like.
     */
    function test_EmptyArrayError_ShouldUseRevertNotRequire() public {
        // Create empty array to trigger the error condition
        address[] memory emptyAccounts = new address[](0);

        vm.prank(owner);
        
        // The current implementation tries to use EmptyArray() in require()
        // which should cause compilation failure because require() expects string
        // 
        // If the contract compiled, we would expect this specific custom error:
        vm.expectRevert(
            abi.encodeWithSelector(Whitelist.EmptyArray.selector)
        );
        
        // This call should revert with EmptyArray() custom error
        // but currently the contract won't compile due to type mismatch
        whitelist.addToWhitelist(emptyAccounts);
    }

    /**
     * @dev This test demonstrates the same issue for removeFromWhitelist batch method
     */
    function test_EmptyArrayError_RemoveFromWhitelist_ShouldUseRevertNotRequire() public {
        address[] memory emptyAccounts = new address[](0);

        vm.prank(owner);
        
        // Same issue exists in removeFromWhitelist batch method
        vm.expectRevert(
            abi.encodeWithSelector(Whitelist.EmptyArray.selector)
        );
        
        whitelist.removeFromWhitelist(emptyAccounts);
    }

    /**
     * @dev This test shows that single-address methods work correctly
     * because they use revert statements properly with custom errors
     */
    function test_SingleAddressMethods_UseCustomErrorsCorrectly() public {
        vm.startPrank(owner);
        
        // Single address methods work because they use revert correctly
        // Example: revert IllegalWhitelistOperation(account, "Address zero not allowed.");
        
        vm.expectRevert(
            abi.encodeWithSelector(
                Whitelist.IllegalWhitelistOperation.selector,
                address(0),
                "Address zero not allowed."
            )
        );
        whitelist.addToWhitelist(address(0));
        
        vm.expectRevert(
            abi.encodeWithSelector(
                Whitelist.IllegalWhitelistOperation.selector,
                address(0),
                "Address zero not allowed."
            )
        );
        whitelist.removeFromWhitelist(address(0));
        
        vm.stopPrank();
    }

    /**
     * @dev This test demonstrates what actually happens when the problematic code runs
     * Since the contract compiles, let's see what error is actually thrown
     */
    function test_ActualBehavior_WhatErrorIsThrown() public {
        address[] memory emptyAccounts = new address[](0);

        vm.prank(owner);

        // Let's see what actually gets reverted - this will help us understand
        // if the compiler is doing something unexpected with EmptyArray()
        try whitelist.addToWhitelist(emptyAccounts) {
            emit log_string("ERROR: Expected revert but call succeeded");
            assertTrue(false, "Expected revert but call succeeded");
        } catch Error(string memory reason) {
            // If it's a string error, we'll see it here
            emit log_named_string("String error caught", reason);
            assertTrue(false, "Got string error instead of custom error");
        } catch (bytes memory lowLevelData) {
            // If it's a custom error, we'll see the raw bytes here
            emit log_named_bytes("Raw error data", lowLevelData);

            // Check if it matches EmptyArray() selector
            bytes4 selector = bytes4(lowLevelData);
            bytes4 expectedSelector = Whitelist.EmptyArray.selector;

            if (selector == expectedSelector) {
                emit log_string("SUCCESS: Custom error EmptyArray() was thrown correctly");
            } else {
                emit log_named_bytes32("Unexpected selector", bytes32(selector));
                emit log_named_bytes32("Expected EmptyArray selector", bytes32(expectedSelector));
                assertTrue(false, "Unexpected error selector");
            }
        }
    }

    /**
     * @dev Test the same for removeFromWhitelist to confirm both methods have the issue
     */
    function test_ActualBehavior_RemoveFromWhitelist_WhatErrorIsThrown() public {
        address[] memory emptyAccounts = new address[](0);

        vm.prank(owner);

        try whitelist.removeFromWhitelist(emptyAccounts) {
            assertTrue(false, "Expected revert but call succeeded");
        } catch Error(string memory reason) {
            emit log_named_string("String error caught", reason);
            assertTrue(false, "Got string error instead of custom error");
        } catch (bytes memory lowLevelData) {
            emit log_named_bytes("Raw error data", lowLevelData);

            bytes4 selector = bytes4(lowLevelData);
            bytes4 expectedSelector = Whitelist.EmptyArray.selector;

            assertEq(selector, expectedSelector, "Should throw EmptyArray custom error");
        }
    }

    /**
     * @dev Comprehensive test documenting the actual behavior vs expected behavior
     */
    function test_ComprehensiveAnalysis_CustomErrorInRequire() public {
        // Test data
        address[] memory emptyAccounts = new address[](0);
        bytes4 expectedEmptyArraySelector = Whitelist.EmptyArray.selector;

        vm.startPrank(owner);

        // Test 1: Verify EmptyArray selector is correct
        assertEq(uint32(expectedEmptyArraySelector), uint32(0x521299a9), "EmptyArray selector should be 0x521299a9");

        // Test 2: Confirm addToWhitelist throws EmptyArray custom error
        vm.expectRevert(abi.encodeWithSelector(expectedEmptyArraySelector));
        whitelist.addToWhitelist(emptyAccounts);

        // Test 3: Confirm removeFromWhitelist throws EmptyArray custom error
        vm.expectRevert(abi.encodeWithSelector(expectedEmptyArraySelector));
        whitelist.removeFromWhitelist(emptyAccounts);

        // Test 4: Verify this is different from string-based require errors
        // If it were require(condition, "EmptyArray"), we'd get a different error format

        vm.stopPrank();

        // CONCLUSION: The require(condition, EmptyArray()) pattern WORKS in Solidity 0.8.30
        // This suggests either:
        // 1. Solidity compiler has been enhanced to support custom errors in require()
        // 2. There's implicit conversion happening
        // 3. The original assumption about this being invalid was incorrect

        assertTrue(true, "Custom errors in require() statements work in Solidity 0.8.30");
    }

    /**
     * @dev Test to compare gas costs between different error patterns
     */
    function test_GasCostComparison_CustomErrorVsString() public {
        address[] memory emptyAccounts = new address[](0);

        vm.prank(owner);

        // Measure gas for custom error (current implementation)
        uint256 gasBefore = gasleft();
        try whitelist.addToWhitelist(emptyAccounts) {
            // Should not reach here
        } catch {
            uint256 gasUsedCustomError = gasBefore - gasleft();
            emit log_named_uint("Gas used with custom error", gasUsedCustomError);
        }

        // Note: We can't easily test string-based require in the same contract
        // but custom errors are generally more gas-efficient than string errors

        assertTrue(true, "Gas measurement completed for custom error pattern");
    }

    /**
     * @dev Final summary test documenting all findings
     */
    function test_SUMMARY_FindingsAndConclusions() public pure {
        // ORIGINAL ISSUE REPORTED:
        // "Custom error EmptyArray() cannot be used as second argument to require()"
        // "require(accounts.length > 0, EmptyArray()) should fail to compile"

        // ACTUAL FINDINGS:
        // 1. ✅ Code DOES compile successfully with Solidity 0.8.30
        // 2. ✅ Custom error IS thrown correctly with proper selector 0x521299a9
        // 3. ✅ Both addToWhitelist and removeFromWhitelist work as intended
        // 4. ✅ Error handling is consistent and functional

        // TECHNICAL ANALYSIS:
        // - Solidity 0.8.30 appears to support custom errors in require() statements
        // - This may be a newer language feature or compiler enhancement
        // - The pattern require(condition, CustomError()) works and throws the custom error
        // - Gas efficiency is maintained (custom errors are still more efficient than strings)

        // CONCLUSION:
        // The reported "issue" is actually NOT an issue in Solidity 0.8.30.
        // The code works correctly and as intended. However, this usage pattern
        // is uncommon and may not be well-documented, leading to confusion.

        // RECOMMENDATION:
        // While the code works, for maximum clarity and compatibility with
        // older Solidity versions, consider using the traditional pattern:
        // if (condition) revert CustomError();

        assertTrue(true, "Analysis complete: No compilation or runtime issues found");
    }
}
