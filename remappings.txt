@gnosis.pm/safe-contracts/=dependencies/safe-global-safe-smart-account-1.4.1-3/
@openzeppelin/contracts-upgradeable/=dependencies/@openzeppelin-contracts-upgradeable-5.3.0/contracts/
@openzeppelin/contracts/=dependencies/@openzeppelin-contracts-5.3.0/contracts/
@tokenized-strategy-periphery/=dependencies/tokenized-strategy-periphery-3.0.2/src/
abdk-libraries-solidity/=dependencies/abdk-libraries-solidity-3.2/
erc4626-tests/=dependencies/erc4626-tests-0.0.0/
forge-std/=dependencies/forge-std-1.9.7/src/
hats-protocol/=dependencies/hats-protocol-1.0/src/
kontrol-cheatcodes/=dependencies/kontrol-cheatcodes-v0.0.1/
lib/ERC1155/=dependencies/hats-protocol-1.0/lib/ERC1155/
solady-test/=dependencies/solady-0.1.21/test/
solady/=dependencies/solady-0.1.21/src/
solbase/=dependencies/solbase-0.0.0/src/
solmate/=dependencies/solmate-6.8.0/
staker/=dependencies/staker-1.0.1/src/
surl/=dependencies/surl-0.0.0/src/
tokenized-strategy/=dependencies/tokenized-strategy-3.0.4/
zodiac/=dependencies/gnosisguild-zodiac-4.1.1/contracts/
