name: 👕 Lint and format

on:
  workflow_call:
    inputs:
      GCP_DOCKER_IMAGE_REGISTRY:
        required: true
        type: string
    secrets:
      GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT:
        required: true

jobs:
  check-formatting:
    name: Check Code Formatting
    runs-on: general
    container:
      image: ${{ inputs.GCP_DOCKER_IMAGE_REGISTRY }}/node-extended:f4f41461
      credentials:
        username: _json_key_base64
        password: ${{ secrets.GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT }}
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Init env
        run: yarn install

      - name: Check formatting
        run: yarn format:check

  lint:
    name: Lint Code
    runs-on: general
    container:
      image: ${{ inputs.GCP_DOCKER_IMAGE_REGISTRY }}/node-extended:f4f41461
      credentials:
        username: _json_key_base64
        password: ${{ secrets.GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT }}
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Init env
        run: yarn install

      - name: Run linter
        run: yarn lint

  lint-commits:
    name: <PERSON>t Commits
    runs-on: general
    container:
      image: ${{ inputs.GCP_DOCKER_IMAGE_REGISTRY }}/node-extended:f4f41461
      credentials:
        username: _json_key_base64
        password: ${{ secrets.GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT }}
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Init env
        run: yarn install

      - name: Run linter
        run: yarn lint:commits

  analyze:
    name: Analyze with Slither
    runs-on: general
    container:
      image: ${{ inputs.GCP_DOCKER_IMAGE_REGISTRY }}/node-extended:f4f41461
      credentials:
        username: _json_key_base64
        password: ${{ secrets.GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Init env
        run: |
          yarn install
          forge soldeer install

      - name: Run Slither
        shell: "bash {0}"
        run: |
          yarn slither:ci
          exit_code=$?
          echo '```' > $GITHUB_STEP_SUMMARY
          cat reports/slither.txt >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          exit $exit_code
