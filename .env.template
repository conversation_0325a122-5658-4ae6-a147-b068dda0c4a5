# Addresses for safe singleton and safe proxy factory deployments on different chain can be found here - https://github.com/safe-global/safe-deployments/tree/main/src/assets/v1.4.1

# Polygon
MODULE_FACTORY=******************************************
MODULE=******************************************
TOKEN=******************************************
POLYGON_RPC_URL=
ETHERSCAN_API_KEY=
SAFE_ADDRESS=
SAFE_DRAGON_VAULT_MODULE_ADDRESS=
CHAIN="polygon" #https://github.com/foundry-rs/forge-std/blob/5a802d7c10abb4bbfb3e7214c75052ef9e6a06f8/src/StdChains.sol#L192
WALLET_TYPE="local"
TEST_SAFE_SINGLETON_POLYGON=******************************************
TEST_SAFE_PROXY_FACTORY_POLYGON=******************************************

# Tests vars (Ethereum fork)
TEST_RPC_URL=https://eth.llamarpc.com
TEST_SAFE_SINGLETON=******************************************
TEST_SAFE_PROXY_FACTORY=******************************************

# Script vars
SAFE_THRESHOLD=1
SAFE_TOTAL_OWNERS=1
SAFE_SINGLETON=******************************************
SAFE_PROXY_FACTORY=******************************************
SAFE_THRESHOLD=1
RPC_URL=https://polygon-rpc.com
OWNER= # owner of safe multisig
PRIVATE_KEY=
RPC_URL=